# ValueInput 组件

一个用于输入和选择数据值的通用组件，支持文本、变量、脚本和可视化函数等多种类型。

## 功能特性

### ✨ 主要功能
- **多种数据类型支持**：文本、变量、脚本、可视化函数
- **智能显示模式**：自动隐藏/显示编辑状态
- **重置功能**：一键重置到默认值
- **类型限制**：可限制允许的数据类型
- **变量模式**：支持仅变量选择模式

### 🎯 美化改进
- **代码结构优化**：清晰的分区和注释
- **计算属性重构**：更好的性能和可读性
- **样式美化**：更现代的UI设计
- **交互优化**：更好的用户体验

## API 接口

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `dataValue` | `FlowDataValue \| undefined` | - | 数据值对象 |
| `dataType` | `string` | - | 数据类型 |
| `onlyVariable` | `boolean` | `false` | 是否只允许变量类型 |
| `limitTypes` | `string[]` | - | 限制的数据类型列表 |
| `autoHideEdit` | `boolean` | `true` | 是否自动隐藏编辑状态 |
| `showReset` | `boolean` | `true` | 是否显示重置按钮 |
| `placeholder` | `string` | `'请输入或选择值'` | 占位符文本 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `readonly` | `boolean` | `false` | 是否只读 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:data-value` | `(value: FlowDataValue)` | 数据值更新 |
| `variable-change` | `(value: FlowDataValue)` | 变量变化 |
| `reset` | `()` | 重置事件 |

### FlowDataValue 接口

```typescript
interface FlowDataValue {
  type: 'text' | 'variable' | 'script' | 'visual';
  dataType?: string;
  textValue?: string;
  variableType?: 'current' | 'local' | 'global' | '';
  variableValue?: string;
  variableName?: string;
  scriptValue?: string;
  scriptName?: string;
  visualSteps?: VisualFunctionStep[];
  visualName?: string;
}
```

## 使用示例

### 基本用法

```vue
<template>
  <value-input 
    v-model:data-value="dataValue" 
    placeholder="请输入值"
    @reset="onReset"
  />
</template>

<script setup>
import { ref } from 'vue';
import ValueInput from './ValueInput.vue';

const dataValue = ref({
  type: 'text',
  textValue: ''
});

const onReset = () => {
  console.log('值已重置');
};
</script>
```

### 仅变量模式

```vue
<template>
  <value-input 
    v-model:data-value="variableValue" 
    :only-variable="true"
    placeholder="请选择变量"
  />
</template>

<script setup>
const variableValue = ref({
  type: 'variable',
  variableType: 'current',
  variableName: '',
  variableValue: ''
});
</script>
```

### 限制数据类型

```vue
<template>
  <value-input 
    v-model:data-value="limitedValue" 
    :limit-types="['string', 'number']"
    placeholder="仅支持字符串和数字"
  />
</template>
```

### 禁用重置功能

```vue
<template>
  <value-input 
    v-model:data-value="dataValue" 
    :show-reset="false"
    placeholder="无重置按钮"
  />
</template>
```

### 始终显示编辑状态

```vue
<template>
  <value-input 
    v-model:data-value="dataValue" 
    :auto-hide-edit="false"
    placeholder="始终显示编辑状态"
  />
</template>
```

## 样式定制

组件提供了丰富的CSS类名用于样式定制：

- `.value-input-container` - 容器
- `.value-input` - 输入框
- `.value-input-text` - 显示文本
- `.reset-button` - 重置按钮
- `.suffix-icons` - 后缀图标容器

## 更新日志

### v2.0.0 (当前版本)
- ✨ 新增重置功能
- 🎨 代码结构优化和美化
- 🔧 改进计算属性和方法组织
- 💄 UI样式美化
- 📝 完善注释和文档

### v1.0.0
- 🎉 初始版本
- 基本的值输入功能
- 支持多种数据类型
