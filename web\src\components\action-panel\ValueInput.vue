<template>
  <div
    class="value-input-container"
    :title="description"
    @click="onClickShowMore"
    @mouseover.stop="showEdit = true"
    @mouseleave.stop="showEdit = false"
    @blur="showEdit = false"
  >
    <!-- 编辑模式输入框 -->
    <t-input v-if="shouldShowInput" v-bind="targetAttrs" class="value-input">
      <template #suffixIcon>
        <div class="suffix-icons">
          <t-button
            v-if="showResetButton"
            variant="text"
            size="small"
            @click.stop="onReset"
            :title="'清除'"
            class="reset-button"
          >
            <template #icon>
              <close-icon />
            </template>
          </t-button>
          <more-icon v-if="!autoHideEdit" />
        </div>
      </template>
    </t-input>

    <!-- 显示模式文本 -->
    <div v-else :class="displayTextClass">
      <div v-if="description" class="value-text">
        {{ description }}
      </div>
      <div v-else class="value-input-text-placeholder">
        {{ placeholder }}
      </div>

      <!-- 重置按钮（显示模式） -->
      <t-button
        v-if="showResetButton && showEdit"
        variant="text"
        size="small"
        @click.stop="onReset"
        :title="'清除'"
        class="reset-button-display"
      >
        <template #icon>
          <close-icon />
        </template>
      </t-button>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'ValueInput',
};
</script>
<script setup lang="ts">
import { cloneDeep } from 'lodash-es';
import { MoreIcon, CloseIcon } from 'tdesign-icons-vue-next';
import { InputProps } from 'tdesign-vue-next';
import { computed, ref, useAttrs, watch, watchEffect } from 'vue';

import { useActionFlowStore } from '@/components/action-panel/store/index';

import { FlowDataValue } from './model';
import { getRandomId } from './utils';

// ==================== 常量定义 ====================
const DEFAULT_DATA_VALUE: FlowDataValue = {
  type: 'text',
  dataType: '',
  variableType: '',
  variableName: '',
  variableValue: '',
  textValue: '',
  scriptName: '',
  scriptValue: '',
};

// ==================== 组件状态 ====================
const showEdit = ref(false);
const actionFlowStore = useActionFlowStore();

// ==================== 接口定义 ====================
interface ValueInputProps extends Omit<InputProps, 'options'> {
  /** 数据值对象 */
  dataValue: FlowDataValue | undefined;
  /** 数据类型 */
  dataType?: string;
  /** 是否只允许变量类型 */
  onlyVariable?: boolean;
  /** 限制的数据类型列表 */
  limitTypes?: string[];
  /** 是否自动隐藏编辑状态 */
  autoHideEdit?: boolean;
  /** 是否显示重置按钮 */
  showReset?: boolean;
}

// ==================== Props 和 Emits ====================
const props = withDefaults(defineProps<ValueInputProps>(), {
  size: 'small',
  readonly: false,
  borderless: true,
  placeholder: '请输入或选择值',
  autoHideEdit: true,
  showReset: true,
});

const emits = defineEmits<{
  'update:data-value': [value: FlowDataValue];
  'variable-change': [value: FlowDataValue];
  reset: [];
}>();

const attrs: Partial<ValueInputProps> = useAttrs();

// ==================== 数据初始化 ====================
/**
 * 创建默认数据值
 */
const createDefaultDataValue = (): FlowDataValue => ({
  ...DEFAULT_DATA_VALUE,
  type: props.onlyVariable ? 'variable' : 'text',
  dataType: props.dataType || props.limitTypes?.join(','),
});

const dataValue = ref<FlowDataValue>(props.dataValue || createDefaultDataValue());

// 监听 props 变化，同步更新数据值
watchEffect(() => {
  dataValue.value = props.dataValue || createDefaultDataValue();
});

// ==================== 计算属性 ====================
/**
 * 获取显示描述文本
 */
const description = computed(() => {
  const { type, textValue, variableName, variableValue, scriptName, visualName } = dataValue.value;

  switch (type) {
    case 'text':
      return textValue || '';
    case 'variable':
      return variableName || variableValue || '';
    case 'script':
      return scriptName || '未命名脚本';
    case 'visual':
      return visualName || '可视化函数';
    default:
      return '';
  }
});

/**
 * 是否显示输入框
 */
const shouldShowInput = computed(() => {
  return (props.autoHideEdit && showEdit.value) || !props.autoHideEdit;
});

/**
 * 显示文本的样式类
 */
const displayTextClass = computed(() => {
  return ['value-input-text', { 'is-disabled': props.disabled }];
});

/**
 * 是否显示重置按钮
 */
const showResetButton = computed(() => {
  return props.showReset && hasValue.value;
});

/**
 * 是否有值
 */
const hasValue = computed(() => {
  const { type, textValue, variableName, variableValue, scriptName, visualName } = dataValue.value;

  switch (type) {
    case 'text':
      return Boolean(textValue);
    case 'variable':
      return Boolean(variableName || variableValue);
    case 'script':
      return Boolean(scriptName);
    case 'visual':
      return Boolean(visualName);
    default:
      return false;
  }
});

/**
 * 目标属性
 */
const targetAttrs = computed<ValueInputProps>(() => {
  return {
    ...attrs,
    ...props,
    modelValue: description.value,
  };
});

// ==================== 方法定义 ====================
const valueId = ref('');

/**
 * 点击显示更多选项
 */
const onClickShowMore = () => {
  actionFlowStore.isSaveValue = false;
  actionFlowStore.showValueDialog = true;
  valueId.value = getRandomId();
  actionFlowStore.currentValueInputData = {
    id: valueId.value,
    value: cloneDeep(dataValue.value),
    onlyVariable: props.onlyVariable,
    limitTypes: props.limitTypes,
  };
};

/**
 * 重置数据值
 */
const onReset = () => {
  const defaultValue = createDefaultDataValue();
  dataValue.value = defaultValue;
  emits('update:data-value', defaultValue);
  emits('variable-change', defaultValue);
  emits('reset');
};

// ==================== 监听器 ====================
/**
 * 监听保存状态变化
 */
watch(
  () => actionFlowStore.isSaveValue,
  (newVal, oldVal) => {
    if (newVal !== oldVal && newVal && actionFlowStore.currentValueInputData?.id === valueId.value) {
      emits('update:data-value', actionFlowStore.currentValueInputData.value);
      emits('variable-change', actionFlowStore.currentValueInputData.value);
    }
  },
);
</script>
<style lang="less" scoped>
// ==================== 容器样式 ====================
.value-input-container {
  width: 100%;
  position: relative;
}

// ==================== 输入框样式 ====================
.value-input {
  :deep(> .t-input) {
    cursor: pointer;

    > .t-input__inner {
      cursor: pointer;
    }
  }
}

// ==================== 后缀图标样式 ====================
.suffix-icons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.reset-button {
  padding: 0;
  min-width: auto;
  width: 16px;
  height: 16px;

  :deep(.t-button__icon) {
    font-size: 12px;
  }

  &:hover {
    background-color: var(--td-bg-color-container-hover);
  }
}

// ==================== 显示文本样式 ====================
.value-input-text {
  font-size: 12px;
  height: 24px;
  padding: 1px 8px;
  border: 1px solid transparent;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  position: relative;
  color: var(--td-text-color-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;

  &.is-disabled {
    border: none;
    background-color: var(--td-bg-color-component-disabled);
  }
}

.value-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.value-input-text-placeholder {
  color: var(--td-text-color-placeholder);
  flex: 1;
}

// ==================== 显示模式重置按钮 ====================
.reset-button-display {
  padding: 0;
  min-width: auto;
  width: 16px;
  height: 16px;
  margin-left: 4px;
  flex-shrink: 0;

  :deep(.t-button__icon) {
    font-size: 12px;
  }

  &:hover {
    background-color: var(--td-bg-color-container-hover);
  }
}
</style>
