<template>
  <div
    class="value-input-container"
    :title="description"
    @click="onClickShowMore"
    @mouseover.stop="showEdit = true"
    @mouseleave.stop="showEdit = false"
    @blur="showEdit = false"
  >
    <!-- 编辑模式输入框 -->
    <t-input v-if="shouldShowInput" v-bind="targetAttrs" class="value-input">
      <template #suffixIcon>
        <div class="suffix-icons">
          <!-- 复制按钮 -->
          <t-button
            v-if="showCopyButton"
            variant="text"
            size="small"
            @click.stop="onCopy"
            :title="'复制'"
            class="action-button"
          >
            <template #icon>
              <copy-icon />
            </template>
          </t-button>

          <!-- 粘贴按钮 -->
          <t-button
            v-if="showPasteButton"
            variant="text"
            size="small"
            @click.stop="onPaste"
            :title="'粘贴'"
            class="action-button"
          >
            <template #icon>
              <paste-icon />
            </template>
          </t-button>

          <!-- 清除按钮 -->
          <t-button
            v-if="showResetButton"
            variant="text"
            size="small"
            @click.stop="onReset"
            :title="'清除'"
            class="action-button"
          >
            <template #icon>
              <close-icon />
            </template>
          </t-button>

          <more-icon v-if="!autoHideEdit" />
        </div>
      </template>
    </t-input>

    <!-- 显示模式文本 -->
    <div v-else :class="displayTextClass">
      <div v-if="description" class="value-text">
        {{ description }}
      </div>
      <div v-else class="value-input-text-placeholder">
        {{ placeholder }}
      </div>

      <!-- 操作按钮（显示模式） -->
      <div v-if="showEdit" class="display-actions">
        <!-- 复制按钮 -->
        <t-button
          v-if="showCopyButton"
          variant="text"
          size="small"
          @click.stop="onCopy"
          :title="'复制'"
          class="action-button-display"
        >
          <template #icon>
            <copy-icon />
          </template>
        </t-button>

        <!-- 粘贴按钮 -->
        <t-button
          v-if="showPasteButton"
          variant="text"
          size="small"
          @click.stop="onPaste"
          :title="'粘贴'"
          class="action-button-display"
        >
          <template #icon>
            <paste-icon />
          </template>
        </t-button>

        <!-- 清除按钮 -->
        <t-button
          v-if="showResetButton"
          variant="text"
          size="small"
          @click.stop="onReset"
          :title="'清除'"
          class="action-button-display"
        >
          <template #icon>
            <close-icon />
          </template>
        </t-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'ValueInput',
};
</script>
<script setup lang="ts">
import { cloneDeep } from 'lodash-es';
import { MoreIcon, CloseIcon, CopyIcon, PasteIcon } from 'tdesign-icons-vue-next';
import { InputProps, MessagePlugin } from 'tdesign-vue-next';
import { computed, ref, useAttrs, watch, watchEffect } from 'vue';

import { useActionFlowStore } from '@/components/action-panel/store/index';

import { FlowDataValue } from './model';
import { getRandomId } from './utils';

// ==================== 常量定义 ====================
const DEFAULT_DATA_VALUE: FlowDataValue = {
  type: 'text',
  dataType: '',
  variableType: '',
  variableName: '',
  variableValue: '',
  textValue: '',
  scriptName: '',
  scriptValue: '',
};

// ==================== 组件状态 ====================
const showEdit = ref(false);
const actionFlowStore = useActionFlowStore();

// ==================== 接口定义 ====================
interface ValueInputProps extends Omit<InputProps, 'options'> {
  /** 数据值对象 */
  dataValue: FlowDataValue | undefined;
  /** 数据类型 */
  dataType?: string;
  /** 是否只允许变量类型 */
  onlyVariable?: boolean;
  /** 限制的数据类型列表 */
  limitTypes?: string[];
  /** 是否自动隐藏编辑状态 */
  autoHideEdit?: boolean;
  /** 是否显示重置按钮 */
  showReset?: boolean;
  /** 是否显示复制按钮 */
  showCopy?: boolean;
  /** 是否显示粘贴按钮 */
  showPaste?: boolean;
}

// ==================== Props 和 Emits ====================
const props = withDefaults(defineProps<ValueInputProps>(), {
  size: 'small',
  readonly: false,
  borderless: true,
  placeholder: '请输入或选择值',
  autoHideEdit: true,
  showReset: true,
  showCopy: true,
  showPaste: true,
});

const emits = defineEmits<{
  'update:data-value': [value: FlowDataValue];
  'variable-change': [value: FlowDataValue];
  reset: [];
  copy: [value: FlowDataValue];
  paste: [value: FlowDataValue];
}>();

const attrs: Partial<ValueInputProps> = useAttrs();

// ==================== 数据初始化 ====================
/**
 * 创建默认数据值
 */
const createDefaultDataValue = (): FlowDataValue => ({
  ...DEFAULT_DATA_VALUE,
  type: props.onlyVariable ? 'variable' : 'text',
  dataType: props.dataType || props.limitTypes?.join(','),
});

const dataValue = ref<FlowDataValue>(props.dataValue || createDefaultDataValue());

// 监听 props 变化，同步更新数据值
watchEffect(() => {
  dataValue.value = props.dataValue || createDefaultDataValue();
});

// ==================== 计算属性 ====================
/**
 * 获取显示描述文本
 */
const description = computed(() => {
  const { type, textValue, variableName, variableValue, scriptName, visualName } = dataValue.value;

  switch (type) {
    case 'text':
      return textValue || '';
    case 'variable':
      return variableName || variableValue || '';
    case 'script':
      return scriptName || '未命名脚本';
    case 'visual':
      return visualName || '可视化函数';
    default:
      return '';
  }
});

/**
 * 是否显示输入框
 */
const shouldShowInput = computed(() => {
  return (props.autoHideEdit && showEdit.value) || !props.autoHideEdit;
});

/**
 * 显示文本的样式类
 */
const displayTextClass = computed(() => {
  return ['value-input-text', { 'is-disabled': props.disabled }];
});

/**
 * 是否显示重置按钮
 */
const showResetButton = computed(() => {
  return props.showReset && hasValue.value;
});

/**
 * 是否显示复制按钮
 */
const showCopyButton = computed(() => {
  return props.showCopy && hasValue.value;
});

/**
 * 是否显示粘贴按钮
 */
const showPasteButton = computed(() => {
  return props.showPaste;
});

/**
 * 是否有值
 */
const hasValue = computed(() => {
  const { type, textValue, variableName, variableValue, scriptName, visualName } = dataValue.value;

  switch (type) {
    case 'text':
      return Boolean(textValue);
    case 'variable':
      return Boolean(variableName || variableValue);
    case 'script':
      return Boolean(scriptName);
    case 'visual':
      return Boolean(visualName);
    default:
      return false;
  }
});

/**
 * 目标属性
 */
const targetAttrs = computed<ValueInputProps>(() => {
  return {
    ...attrs,
    ...props,
    modelValue: description.value,
  };
});

// ==================== 方法定义 ====================
const valueId = ref('');

/**
 * 点击显示更多选项
 */
const onClickShowMore = () => {
  actionFlowStore.isSaveValue = false;
  actionFlowStore.showValueDialog = true;
  valueId.value = getRandomId();
  actionFlowStore.currentValueInputData = {
    id: valueId.value,
    value: cloneDeep(dataValue.value),
    onlyVariable: props.onlyVariable,
    limitTypes: props.limitTypes,
  };
};

/**
 * 重置数据值
 */
const onReset = () => {
  const defaultValue = createDefaultDataValue();
  dataValue.value = defaultValue;
  emits('update:data-value', defaultValue);
  emits('variable-change', defaultValue);
  emits('reset');
};

/**
 * 复制数据值到剪贴板
 */
const onCopy = async () => {
  try {
    const copyData = {
      type: 'ValueInput',
      data: cloneDeep(dataValue.value),
    };

    await navigator.clipboard.writeText(JSON.stringify(copyData));
    emits('copy', dataValue.value);

    // 显示成功提示
    MessagePlugin.success('已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    MessagePlugin.error('复制失败');
  }
};

/**
 * 从剪贴板粘贴数据值
 */
const onPaste = async () => {
  try {
    const clipboardText = await navigator.clipboard.readText();

    if (!clipboardText) {
      MessagePlugin.warning('剪贴板为空');
      return;
    }

    let pasteData;
    try {
      pasteData = JSON.parse(clipboardText);
    } catch {
      MessagePlugin.warning('剪贴板内容格式不正确');
      return;
    }

    // 验证是否为 ValueInput 数据
    if (pasteData.type === 'ValueInput' && pasteData.data) {
      const newValue = pasteData.data as FlowDataValue;

      // 验证数据类型限制
      if (props.onlyVariable && newValue.type !== 'variable') {
        MessagePlugin.warning('当前模式只支持变量类型');
        return;
      }

      if (props.limitTypes && props.limitTypes.length > 0) {
        const dataType = newValue.dataType || newValue.type;
        const isValidType = props.limitTypes.some((type) => dataType?.includes(type) || dataType === type);

        if (!isValidType) {
          MessagePlugin.warning(`数据类型不匹配，仅支持: ${props.limitTypes.join(', ')}`);
          return;
        }
      }

      dataValue.value = newValue;
      emits('update:data-value', newValue);
      emits('variable-change', newValue);
      emits('paste', newValue);

      MessagePlugin.success('粘贴成功');
    } else {
      // 尝试作为纯文本粘贴
      const textValue: FlowDataValue = {
        ...createDefaultDataValue(),
        type: 'text',
        textValue: clipboardText,
      };

      dataValue.value = textValue;
      emits('update:data-value', textValue);
      emits('variable-change', textValue);
      emits('paste', textValue);

      MessagePlugin.success('已粘贴为文本');
    }
  } catch (error) {
    console.error('粘贴失败:', error);
    MessagePlugin.error('粘贴失败，请检查剪贴板权限');
  }
};

// ==================== 监听器 ====================
/**
 * 监听保存状态变化
 */
watch(
  () => actionFlowStore.isSaveValue,
  (newVal, oldVal) => {
    if (newVal !== oldVal && newVal && actionFlowStore.currentValueInputData?.id === valueId.value) {
      emits('update:data-value', actionFlowStore.currentValueInputData.value);
      emits('variable-change', actionFlowStore.currentValueInputData.value);
    }
  },
);
</script>
<style lang="less" scoped>
// ==================== 容器样式 ====================
.value-input-container {
  width: 100%;
  position: relative;
}

// ==================== 输入框样式 ====================
.value-input {
  :deep(> .t-input) {
    cursor: pointer;

    > .t-input__inner {
      cursor: pointer;
    }
  }
}

// ==================== 后缀图标样式 ====================
.suffix-icons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-button {
  padding: 0;
  min-width: auto;
  width: 16px;
  height: 16px;

  :deep(.t-button__icon) {
    font-size: 12px;
  }

  &:hover {
    background-color: var(--td-bg-color-container-hover);
  }
}

// ==================== 显示文本样式 ====================
.value-input-text {
  font-size: 12px;
  height: 24px;
  padding: 1px 8px;
  border: 1px solid transparent;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  position: relative;
  color: var(--td-text-color-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;

  &.is-disabled {
    border: none;
    background-color: var(--td-bg-color-component-disabled);
  }
}

.value-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.value-input-text-placeholder {
  color: var(--td-text-color-placeholder);
  flex: 1;
}

// ==================== 显示模式操作按钮 ====================
.display-actions {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-left: 4px;
  flex-shrink: 0;
}

.action-button-display {
  padding: 0;
  min-width: auto;
  width: 16px;
  height: 16px;

  :deep(.t-button__icon) {
    font-size: 12px;
  }

  &:hover {
    background-color: var(--td-bg-color-container-hover);
  }
}
</style>
